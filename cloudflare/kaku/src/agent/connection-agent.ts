import {
  Agent,
  AgentContext,
  type Connection,
  type ConnectionContext,
  type WSMessage,
} from 'agents';
import { html } from 'hono/html';
import {
  refreshTensorFlowBoundingBox,
  startCaptchaMonitoring,
  stopCaptchaMonitoring,
} from '../browser';
import { CDP } from '../browser/simple-cdp';
import {
  CDPAttachedToTargetParams,
  CDPConsoleAPIParams,
  CDPEvent,
  CDPRuntimeBindingCalledParams,
  CDPRuntimeExceptionParams,
} from '../browser/types/cdp-events';
import {
  ErrorCollector,
  ErrorContext,
  ErrorDisplay,
  ErrorRouter,
  ErrorService,
} from '../common/error';
import { encryptData, getEncryptionKey } from '../common/utils';
import { templateEngine } from '../ui';
import { generateActionsFromExtractionResult } from '../workflow/utils/helpers';
import { PlatformTypes } from '../ui/constants';
import { BrowserServiceFactory, RemoteBrowserService } from '../workflow/services';
import { LLMService } from '../llm/LLMService';
import { OpenAILLMRepository } from '../llm/OpenAILLMRepository';
import { AnthropicLLMRepository } from '../llm/AnthropicLLMRepository';
import { AgentState, ExtractResult } from './types';
import { BoundingRect, CropBoxUpdateData, PageStatus } from './types/agent-state';
import {
  ActionWithOptionalCoordinates,
  CaptchaBoundingBox,
  PageStateResultWithOptionalCoordinates,
  PageStateResult,
} from './types/extract-result';
import {
  CoordinateResolutionService,
  ElementCoordinateRequest,
} from './services/coordinate-resolution';
import { FormVisionResult } from '../form-generation/htmx-generator';
import {
  FormSubmissionEvent as WorkflowFormSubmissionEvent,
  FormSubmissionPayloadSource,
} from '../workflow/types/ConnectionsWorkflowParams';
import {
  BaseWebSocketEvent,
  FormSubmissionEvent as WebSocketFormSubmissionEvent,
} from './types/websocket-events';
import { FormSubmissionPayload } from './types/form-types';
import { COORDINATOR_CONSTANTS } from '../shared/coordinator-types';

export class Connections extends Agent<Env, AgentState> {
  initialState: AgentState = {
    userId: 'unknown',
    platformId: 'kazeel',
    referenceId: '',
    status: PageStatus.INITIAL,
    captchaSetupComplete: false,
    initializationStatus: 'initial',
    termsAndConditionsApproved: false,
  };
  offererConn: Connection | null = null;
  answererConn: Connection | null = null;
  cdpClient: CDP | null = null;
  targetSessionId: string | null = null;
  private cdpErrorHandlers: (() => void) | null = null;
  private browserService: RemoteBrowserService = BrowserServiceFactory.createFromEnvironment(
    this.env,
  );
  llmService: LLMService;
  coordinateResolutionService: CoordinateResolutionService;

  // Logging configuration
  private config = {
    debug: true, // Set to true for verbose logging
  };

  constructor(ctx: AgentContext, env: Env) {
    super(ctx, env);
    const openAIRepository = new OpenAILLMRepository(env.OPENAI_API_KEY, env.AI_GATEWAY_OPENAI_URL);
    const anthropicRepository = new AnthropicLLMRepository(
      env.ANTHROPIC_API_KEY,
      env.AI_GATEWAY_ANTHROPIC_URL,
    );
    this.llmService = new LLMService({
      primaryRepo: openAIRepository,
      secondaryRepo: anthropicRepository,
    });
    this.coordinateResolutionService = new CoordinateResolutionService(this.llmService);
  }

  private log(...args: any[]): void {
    if (this.config.debug) {
      console.log('[kazeel][connections-agent]', ...args);
    }
  }

  private warn(...args: any[]): void {
    console.error('[kazeel][connections-agent]', ...args);
  }

  private error(...args: any[]): void {
    console.error('[kazeel][connections-workflow]', ...args);
  }

  /**
   * Initial setup of Durable object
   * Probably called only once when agentDO is created
   * Initializes crucial data required for the agent to process
   */
  public setup(platformId: PlatformTypes, userId: string, referenceId: string): void {
    this.setName(referenceId);
    this.setState({
      ...this.state,
      platformId: platformId,
      userId: userId,
      referenceId: referenceId,
    });
    // cleanup scheduler that runs after an hour late than the Coordinator DO
    // that acts as a fallback if the Coordinator DO fails to do so.
    this.scheduleSelfCleanup();
  }

  private async scheduleSelfCleanup() {
    const deletionTime = Date.now() + COORDINATOR_CONSTANTS.AGENT_DELETION_DELAY + 60 * 60 * 1000;
    this.schedule(new Date(deletionTime), 'deleteAll');
  }

  async alarm(): Promise<void> {
    super.alarm();
  }

  public async eagerlyInitializeResources(
    platform: PlatformTypes,
    userId: string,
    linkId: string,
  ): Promise<void> {
    if (!platform || !userId) {
      console.error('Invalid link');
      this.setState({
        ...this.state,
        initializationStatus: 'failed',
        errorMessage: 'Invalid link',
      });
      return;
    }

    if (this.state.initializationStatus === 'completed') {
      return;
    }

    this.setState({
      ...this.state,
      initializationStatus: 'in_progress',
    });

    try {
      // Create browser session
      const browserSession = await this.browserService.createSession({
        browserArgs: ['--auto-accept-this-tab-capture'],
        device: ['desktop'],
        solveCaptchas: false,
      });

      // Set up CDP client
      this.cdpClient = new CDP({ webSocketDebuggerUrl: browserSession.wsEndpoint });
      this.setupCDPErrorMonitoring();

      await this.cdpClient.Target.setAutoAttach({
        autoAttach: true,
        flatten: true,
        waitForDebuggerOnStart: false,
      });

      // Add event listener for target attachment
      this.cdpClient.Target.addEventListener(
        'attachedToTarget',
        this.onAttachedToTarget.bind(this),
      );

      const instance = await this.env.CONNECTIONS_WORKFLOW.create({
        params: {
          platformId: platform,
          userId,
          sessionId: browserSession.sessionId!,
          linkId: linkId,
        },
      });

      // Update state with session and workflow info, and mark initialization as complete
      this.setState({
        ...this.state,
        sessionId: browserSession.sessionId,
        workflowId: instance.id,
        initializationStatus: 'completed',
        errorMessage: undefined,
      });
    } catch (error) {
      this.setState({
        ...this.state,
        initializationStatus: 'failed',
        errorMessage: error instanceof Error ? error.message : 'Unknown initialization error',
      });

      await ErrorService.handleGenericError(
        'agent',
        'SYSTEM_ERROR',
        error,
        {
          sessionId: 'background_init_failed',
          userId: userId,
          platformId: platform,
          referenceId: linkId,
        },
        this.env,
        'error',
      );
    }
  }

  /**
   * New element-based coordinate resolution method
   */
  async getElementCoordinates(
    screenshot: string,
    formVisionResult: FormVisionResult,
  ): Promise<void> {
    const viewport = await this.cdpClient!.Page.getLayoutMetrics(undefined, this.targetSessionId!);
    const viewportWidth = viewport.cssLayoutViewport.clientWidth;
    const viewportHeight = viewport.cssLayoutViewport.clientHeight;

    const elementRequest: ElementCoordinateRequest = {
      fields: formVisionResult.controls.fields,
      buttons: formVisionResult.controls.buttons,
    };

    const coordinateMapping = await this.coordinateResolutionService.resolveElementCoordinates(
      screenshot,
      elementRequest,
      this.state.platformId,
      viewportWidth,
      viewportHeight,
    );

    // Store the coordinate mapping in state
    this.setState({
      ...this.state,
      elementCoordinateMapping: coordinateMapping,
      coordinateResolutionInProgress: false,
      skipBroadcast: true,
    });
  }

  async onFormStateChange(screenshot: string, result: PageStateResult) {
    let newStatus: AgentState['status'] =
      result.classificationResult.screenInfo.authState === 'authenticated'
        ? PageStatus.COMPLETED
        : PageStatus.WAITING_FOR_HUMAN;

    // Convert to PageStateResultWithOptionalCoordinates for immediate display
    const pageWithoutCoordinates: PageStateResultWithOptionalCoordinates = {
      ...result,
      coordinatesResolved: false,
    };
    const newState: AgentState = {
      ...this.state,
      status: newStatus,
      pageStateResult: pageWithoutCoordinates,
      coordinateResolutionInProgress: true,
      history: this.state?.history ? [...this.state.history, result] : [result],
    };

    this.setState(newState);

    // Start coordinate resolution asynchronously
    const actions = generateActionsFromExtractionResult(result.extractionResult);
    if (
      result.classificationResult.screenInfo.authState !== 'authenticated' &&
      actions.length > 0
    ) {
      // Use new element-based coordinate resolution if FormVisionResult is available
      if (result) {
        const coordinatePromise = this.resolveElementCoordinatesAsync(
          screenshot,
          result.extractionResult,
        );

        this.setState({
          ...this.state,
          coordinateResolutionPromise: coordinatePromise,
          skipBroadcast: true,
        });
      }
    }
  }

  async markWaitingForAgent() {
    this.setState({
      ...this.state,
      status: PageStatus.WAITING_FOR_AGENT,
    });
  }

  /**
   * New element-based coordinate resolution method
   */
  private async resolveElementCoordinatesAsync(
    screenshot: string,
    formVisionResult: FormVisionResult,
  ): Promise<void> {
    try {
      await this.getElementCoordinates(screenshot, formVisionResult);

      // Build field name to ID mapping for form submission
      const fieldNameToIdMapping: Record<string, string> = {};
      formVisionResult.controls.fields.forEach((field) => {
        if (field.name) {
          fieldNameToIdMapping[field.name] = field.id;
        }
      });

      // Mark coordinates as resolved
      if (this.state.pageStateResult) {
        const updatedPage: PageStateResultWithOptionalCoordinates = {
          ...this.state.pageStateResult,
          coordinatesResolved: true,
        };

        this.setState({
          ...this.state,
          pageStateResult: updatedPage,
          fieldNameToIdMapping,
          coordinateResolutionInProgress: false,
          skipBroadcast: true,
        });
      }
    } catch (error) {
      console.error('Phase 2 element coordinate resolution failed:', error);
      this.setState({
        ...this.state,
        coordinateResolutionInProgress: false,
        skipBroadcast: true,
      });
      throw error;
    }
  }

  /**
   * Filter actions based on interaction type to implement selective action execution
   */
  private async filterActionsByInteractionType(
    allActions: ActionWithOptionalCoordinates[],
    interaction?: string,
    clickId?: string,
    formValues?: Record<string, string>,
  ): Promise<ActionWithOptionalCoordinates[]> {
    this.log(
      `[ACTION FILTER] Filtering ${allActions.length} actions for interaction type: ${interaction}`,
    );

    switch (interaction) {
      case 'submit':
        const inputActions = allActions.filter((action) => {
          if (action.type === 'fill' || action.type === 'select') {
            const hasValue =
              formValues &&
              Object.keys(formValues).some((key) => {
                return this.actionCorrespondsToFormValue(action, key, formValues);
              });
            return hasValue;
          }
          return false;
        });

        const submitAction = allActions.find(
          (action) => action.type === 'click' && action.name === clickId,
        );

        const result = [...inputActions];
        if (submitAction) {
          result.push(submitAction);
        }

        const aiActionCount = inputActions.filter((action) =>
          this.isActionForDontAskAgainControl(action),
        ).length;
        const regularActionCount = inputActions.length - aiActionCount;

        this.log(
          `[ACTION FILTER] Form submission: ${regularActionCount} input actions + ${aiActionCount} AI actions + ${submitAction ? 1 : 0} submit action`,
        );
        return result;

      case 'click':
        // For individual button clicks: only the specific button that was clicked
        const buttonAction = allActions.find(
          (action) => action.type === 'click' && action.name === clickId,
        );

        this.log(`[ACTION FILTER] Button click: ${buttonAction ? 1 : 0} action(s)`);
        return buttonAction ? [buttonAction] : [];

      default:
        console.error(`[ACTION FILTER] Invalid or missing interaction type: ${interaction}`);
        await ErrorService.handleGenericError(
          'agent',
          'SYSTEM_ERROR',
          new Error(
            `Invalid interaction type: ${interaction}. Expected: form-submission, button-click, or ai-action`,
          ),
          {
            sessionId: this.targetSessionId ?? 'not_initialized',
            userId: this.state.userId,
            platformId: this.state.platformId,
            referenceId: this.state.referenceId,
          },
          this.env,
          'error',
        );
        return [];
    }
  }

  /**
   * Check if an action corresponds to a form value
   */
  private actionCorrespondsToFormValue(
    action: ActionWithOptionalCoordinates,
    formKey: string,
    formValues: Record<string, string>,
  ): boolean {
    // Direct match by action name (field.id)
    if (action.name === formKey) {
      return true;
    }

    // Check using field name to ID mapping
    if (this.state.fieldNameToIdMapping) {
      const fieldId = this.state.fieldNameToIdMapping[formKey];
      if (fieldId === action.name) {
        return true;
      }
    }

    // Special handling for radio buttons: check if action name matches field-value pattern
    // e.g., formKey='confirmation_method', formValue='whatsapp'
    // should match action.name='notification-on-another-device-whatsapp'
    const formValue = formValues[formKey];
    if (formValue && action.name.endsWith(`-${formValue}`)) {
      return true;
    }

    return false;
  }

  /**
   * Check if an action corresponds to a field with isDontAskAgainControl
   */
  private isActionForDontAskAgainControl(action: ActionWithOptionalCoordinates): boolean {
    if (!this.state.pageStateResult?.extractionResult?.controls?.fields) {
      return false;
    }

    const field = this.state.pageStateResult.extractionResult.controls.fields.find(
      (field) => field.id === action.name,
    );

    return field?.isDontAskAgainControl === true;
  }

  onStateUpdate(state: AgentState | undefined, _source: Connection | 'server'): void {
    if (!state) return;
    // Skip broadcasting if skipBroadcast flag is set
    if (state.skipBroadcast) {
      // Reset the flag directly in the state object to avoid recursive setState calls
      state.skipBroadcast = false;
      return;
    }

    const uiUpdate = html` <div id="connection-flow" hx-swap-oob="innerHTML">
      ${templateEngine.generateContent(state, this.state.platformId)}
    </div>`;

    this.broadcast(uiUpdate as string);
  }

  arrayBufferToBase64 = (buffer: ArrayBuffer) => Buffer.from(buffer).toString('base64');

  async onConnect(connection: Connection, _ctx: ConnectionContext): Promise<void> {
    this.log(`Connected: to ${this.name}. Initial status is ${this.state.status}`, connection.id);
    // State of interactivity for the end user. Can be paused or enabled.
    if (
      this.state.status === 'waiting-for-human' &&
      this.state.interactivity?.status === 'enabled'
    ) {
      this.broadcast(
        JSON.stringify({
          type: 'interactivity-status',
          status: this.state.interactivity.status,
          cropBox: this.state.interactivity.cropBox,
          inputBoxRects: this.state.interactivity.inputBoxRects,
        }),
      );
    }
    if (this.state.status === PageStatus.INITIAL) {
      //Begin the flow
      await this.handleFlowInitiate({
        platform: this.state.platformId,
      });
    }

    const uiUpdate = html` <div id="connection-flow" hx-swap-oob="innerHTML">
      ${templateEngine.generateContent(this.state, this.state.platformId)}
    </div>`;
    this.broadcast(uiUpdate as string);
  }

  onClose(connection: Connection): void | Promise<void> {
    this.log(`Disconnected from ${this.name}`, connection.id);

    this.cleanupCDPErrorMonitoring();
  }

  override async onMessage(connection: Connection, message: WSMessage) {
    if (typeof message !== 'string') {
      console.error('Invalid message type received:', typeof message);
      return;
    }

    try {
      const event = JSON.parse(message) as BaseWebSocketEvent;
      this.log(`Received WebSocket event: ${event.type}`);
      await this.handleWebSocketEvent(event, connection);
    } catch (error) {
      console.error('Error processing WebSocket message:', error);
      throw new Error(
        `Failed to process WebSocket message: ${message}, Error: ${JSON.stringify(error)}`,
      );
    }
  }

  /**
   * Handle WebSocket events with type safety and consistent routing
   */
  private async handleWebSocketEvent(
    event: BaseWebSocketEvent,
    connection: Connection,
  ): Promise<void> {
    switch (event.type) {
      case 'agree_and_continue':
        await this.handleFlowInitiate({
          platform: this.state.platformId,
        });
        break;

      case 'decline_terms':
        await this.handleTermsDecline();
        break;

      case 'form_submission': {
        const formEvent = event as WebSocketFormSubmissionEvent;
        await this.handleInputSubmitted(connection, formEvent);
        break;
      }

      case 'cropbox-update': {
        const cropEvent = event as CropBoxUpdateData;
        await this.handleCropBoxUpdate(cropEvent);
        break;
      }

      case 'retry':
        await this.handleRetry();
        break;

      default:
        this.log('Received unknown event type:', event.type);
        this.broadcast(JSON.stringify({ ...event }), [connection.id]);
        break;
    }
  }

  async deleteAll() {
    await this.destroy();
  }

  async onAttachedToTarget({ params }: CDPEvent<CDPAttachedToTargetParams>) {
    // get session ID
    const { sessionId, targetInfo } = params;

    if (targetInfo.type === 'page') {
      this.targetSessionId = sessionId;
      await this.cdpClient!.Page.enable(undefined, sessionId);
      await this.cdpClient!.Runtime.enable(undefined, sessionId);
    }
  }

  injectMouseTracker = async () => {
    if (!this.cdpClient) {
      console.error('CDP client not initialized');
      return;
    }

    await this.cdpClient.Runtime.evaluate({
      expression: `
      (function () {
        console.log('[KAZEEL] Injecting mouse tracker');
        document.addEventListener('mousemove', (event) => {
          const marker = document.createElement('div');
          Object.assign(marker.style, {
            position: 'absolute',
            left: event.clientX + 'px',
            top: event.clientY + 'px',
            width: '6px',
            height: '6px',
            borderRadius: '50%',
            backgroundColor: 'rgba(255, 0, 0, 0.4)',
            zIndex: '999999',
            pointerEvents: 'none',
            transform: 'translate(-50%, -50%)',
          });
          document.body.appendChild(marker);
          setTimeout(() => marker.remove(), 500);
        });
      })();
    `,
      awaitPromise: true,
    });
  };

  async handleFlowInitiate(config: { platform: PlatformTypes }) {
    if (
      this.state.initializationStatus !== PageStatus.COMPLETED &&
      !this.state.termsAndConditionsApproved
    )
      return;

    // Mark that user has agreed to terms and conditions
    this.setState({
      ...this.state,
      termsAndConditionsApproved: true,
    });

    const checkInitialization = async (): Promise<void> => {
      return new Promise((resolve, reject) => {
        const poll = () => {
          if (this.state.initializationStatus === 'completed') {
            resolve();
          } else if (this.state.initializationStatus === 'failed') {
            console.error('Background initialization failed:', this.state.errorMessage);
            reject(
              new Error(`Initialization failed: ${this.state.errorMessage || 'Unknown error'}`),
            );
          } else {
            setTimeout(poll, 100);
          }
        };
        poll();
      });
    };

    try {
      await checkInitialization();
      // At this point, initialization is complete and we can proceed
      this.log('Flow initiation completed successfully');
    } catch (error) {
      const userId = this.state.userId;
      await ErrorService.handleGenericError(
        'agent',
        'SYSTEM_ERROR',
        error,
        {
          sessionId: 'flow_initiate_failed',
          userId: userId,
          platformId: config.platform,
          referenceId: this.state.referenceId,
        },
        this.env,
        'error',
      );
    }
  }

  /**
   * Handle when user declines terms and conditions
   */
  async handleTermsDecline(): Promise<void> {
    this.log('User declined terms and conditions');
    this.setState({
      ...this.state,
      termsAndConditionsApproved: false,
      status: PageStatus.INITIAL,
    });
  }

  async handleAndDisplayError(errorContext: ErrorContext): Promise<void> {
    const processedError = ErrorService.processErrorForUI(errorContext);

    this.setState({
      ...this.state,
      status: PageStatus.ERROR,
      errorMessage: processedError.userMessage,
    });

    const errorUI = html` <div id="connection-flow" hx-swap-oob="innerHTML">
      ${ErrorDisplay(processedError)}
    </div>`;

    this.broadcast(errorUI as string);
  }

  private async handleInputSubmitted(_: Connection, payload: FormSubmissionPayload) {
    try {
      if (this.state.status !== 'waiting-for-human') return;
      if (!this.state.workflowId) {
        await ErrorService.handleGenericError(
          'agent',
          'SYSTEM_ERROR',
          new Error('workflowID should not be null'),
          {
            sessionId: this.targetSessionId ?? 'not_initialized',
            userId: this.state.userId,
            platformId: this.state.platformId,
            referenceId: this.state.referenceId,
          },
          this.env,
          'error',
        );
        return;
      }
      this.setState({ ...this.state, status: PageStatus.WAITING_FOR_AGENT });
      const { HEADERS, clickId, interaction, action, actor, ...formValues } = payload;
      const workflowId = this.state.workflowId;
      // Validate required interaction metadata
      if (!clickId) {
        await ErrorService.handleGenericError(
          'agent',
          'SYSTEM_ERROR',
          new Error('Missing required field: clickId'),
          {
            sessionId: this.targetSessionId ?? 'not_initialized',
            userId: this.state.userId,
            platformId: this.state.platformId,
            referenceId: this.state.referenceId,
          },
          this.env,
          'error',
        );
        return;
      }
      if (!interaction) {
        await ErrorService.handleGenericError(
          'agent',
          'SYSTEM_ERROR',
          new Error('Missing required field: interaction'),
          {
            sessionId: this.targetSessionId ?? 'not_initialized',
            userId: this.state.userId,
            platformId: this.state.platformId,
            referenceId: this.state.referenceId,
          },
          this.env,
          'error',
        );
        return;
      }

      this.log(`[INTERACTION] Type: ${interaction}, Clicked Button: ${clickId}`);

      if (this.state.coordinateResolutionInProgress && this.state.coordinateResolutionPromise) {
        await this.state.coordinateResolutionPromise;
      }

      const allActions = this.state.pageStateResult
        ? generateActionsFromExtractionResult(this.state.pageStateResult.extractionResult)
        : [];

      // Filter actions based on interaction type
      const finalActions = await this.filterActionsByInteractionType(
        allActions,
        interaction,
        clickId,
        formValues,
      );

      // Use element-based coordinate mapping if available, otherwise fall back to action coordinates
      const actions = finalActions.map((action) => {
        let coordinates = action.coordinates;

        // If we have element coordinate mapping, use it to lookup coordinates by action name (field.id)
        if (this.state.elementCoordinateMapping && action.name) {
          const elementCoord = this.state.elementCoordinateMapping[action.name];

          if (elementCoord) {
            coordinates = elementCoord;
            this.log(
              `[COORDINATE MAPPING] Found coordinates for action "${action.name}":`,
              coordinates,
            );
          } else {
            console.warn(
              `[COORDINATE MAPPING] No coordinates found for action "${action.name}". Available elements:`,
              Object.keys(this.state.elementCoordinateMapping),
            );
          }
        }

        // Map form values for fill actions
        // Note: action.name is field.id, but form values come by HTML name attribute
        // Use the stored fieldNameToIdMapping to find the correct form value
        if (action.type === 'fill' && action.name) {
          let value: string | undefined;

          // Find the HTML name attribute that corresponds to this field ID
          if (this.state.fieldNameToIdMapping) {
            // Find the name that maps to this field ID
            const fieldName = Object.keys(this.state.fieldNameToIdMapping).find(
              (name) => this.state.fieldNameToIdMapping![name] === action.name,
            );

            if (fieldName) {
              value = formValues[fieldName];
            }
          }

          // Fallback: try direct lookup by action name (field.id)
          if (!value) {
            value = formValues[action.name];
          }

          if (value) {
            return {
              ...action,
              coordinates,
              value,
            };
          }
        }

        return {
          ...action,
          coordinates,
        };
      });

      // Check for actions with missing coordinates and handle with ErrorService
      const actionsWithMissingCoordinates = actions.filter(
        (action) =>
          'isActionWithoutCoordinates' in action && action.isActionWithoutCoordinates === true,
      );
      if (actionsWithMissingCoordinates.length > 0) {
        const missingActionNames = actionsWithMissingCoordinates
          .map((action) => action.name)
          .join(', ');
        await ErrorService.handleGenericError(
          'agent',
          'SYSTEM_ERROR',
          new Error(`Actions missing coordinates: ${missingActionNames}`),
          {
            sessionId: this.targetSessionId ?? 'not_initialized',
            userId: this.state.userId,
            platformId: this.state.platformId,
            referenceId: this.state.referenceId,
          },
          this.env,
          'error',
        );
        return;
      }

      const encryptionKey = await getEncryptionKey();
      const encrypted = await encryptData(JSON.stringify({ actions }), encryptionKey);

      const workflow = await this.env.CONNECTIONS_WORKFLOW.get(workflowId);
      const payloadWithCoordinates: WorkflowFormSubmissionEvent = {
        payload: encrypted,
        coordinates: this.state.elementCoordinateMapping,
        source: FormSubmissionPayloadSource.PAGE_FORM_SUBMISSION,
      };
      await workflow.sendEvent({
        type: 'form-submission',
        payload: payloadWithCoordinates,
      });
    } catch (error) {
      console.error('Failed to handle form submission:', error);

      await ErrorService.handleGenericError(
        'agent',
        'SYSTEM_ERROR',
        error,
        {
          sessionId: this.targetSessionId ?? 'not_initialized',
          userId: this.state.userId,
          platformId: this.state.platformId,
          referenceId: this.state.referenceId,
        },
        this.env,
        'error',
      );
    }
  }

  async sendTwoFactorAuthenticationCompletedEvent() {
    const workflowId = this.state.workflowId;
    if (workflowId) {
      const workflow = await this.env.CONNECTIONS_WORKFLOW.get(workflowId);
      await workflow.sendEvent({
        type: 'form-submission',
        payload: {
          payload: '',
          source: FormSubmissionPayloadSource.TWO_FACTOR_AUTHENTICATION_COMPLETION,
        },
      });
    } else {
      new Error('workflowID should not be null');
    }
  }

  async handleCaptchaSolvedEvent(differenceData: {
    differencePercentage: number;
    timestamp: string;
    executionContextId: number;
    source?: string;
  }) {
    try {
      this.log(
        `Captcha solved event received, source: ${differenceData.source} - ${differenceData.differencePercentage.toFixed(2)}%`,
      );
      if (!this.state.workflowId) {
        await ErrorService.handleGenericError(
          'agent',
          'SYSTEM_ERROR',
          new Error('workflowID should not be null'),
          {
            sessionId: this.targetSessionId ?? 'not_initialized',
            userId: this.state.userId,
            platformId: this.state.platformId,
            referenceId: this.state.referenceId,
          },
          this.env,
          'error',
        );
        return;
      }

      // Send captcha solved notification to workflow
      const workflowId = this.state.workflowId;
      const workflow = await this.env.CONNECTIONS_WORKFLOW.get(workflowId);
      await workflow.sendEvent({
        type: 'captcha-solved',
        payload: differenceData,
      });
      await this.stopInteractivity();
    } catch (error) {
      console.error('Failed to handle captcha solved event:', error);

      await ErrorService.handleGenericError(
        'agent',
        'SYSTEM_ERROR',
        error,
        {
          sessionId: this.targetSessionId ?? 'not_initialized',
          userId: this.state.userId,
          platformId: this.state.platformId,
          referenceId: this.state.referenceId,
        },
        this.env,
        'error',
      );
    }
  }

  async stopInteractivity() {
    this.log('stopping interactivity now..');
    this.setState({
      ...this.state,
      status: PageStatus.WAITING_FOR_AGENT,
      interactivity: {
        ...this.state.interactivity!,
        status: 'completed',
      },
    });
    this.broadcast(JSON.stringify({ type: 'interactivity-status', status: 'completed' }));
    if (this.cdpClient) {
      await stopCaptchaMonitoring(this.cdpClient, this.targetSessionId!);
    }
  }

  async handleCropBoxUpdate(data: CropBoxUpdateData) {
    if (this.state.interactivity?.status === 'completed') {
      return;
    }
    try {
      const newCropBox = data.cropBox;
      const inputBoxRects = data.inputBoxRects;

      if (this.isCaptchaBoundingBox(newCropBox) && this.isBoundingRectArray(inputBoxRects)) {
        this.setState({
          ...this.state,
          interactivity: {
            status: 'enabled',
            cropBox: newCropBox,
            inputBoxRects,
          },
        });
        this.broadcast(
          JSON.stringify({
            type: 'interactivity-status',
            status: 'enabled',
            cropBox: newCropBox,
            inputBoxRects,
          }),
        );
      } else {
        throw Error(`Invalid cropbox input type: ${JSON.stringify(data)}`);
      }
    } catch (error) {
      console.error('Failed to handle CropBoxUpdate event:', error);

      await ErrorService.handleGenericError(
        'agent',
        'SYSTEM_ERROR',
        error,
        {
          sessionId: this.targetSessionId ?? 'not_initialized',
          userId: this.state.userId,
          platformId: this.state.platformId,
          referenceId: this.state.referenceId,
        },
        this.env,
        'error',
      );
    }
  }

  async handleCaptchaDetected(
    executionContextId: number,
    viewport: { width: number; height: number },
  ) {
    if (!this.cdpClient) {
      console.error('Cannot handle captcha detected: cdpClient not initialized');
      return;
    }

    if (this.state.status === 'error') {
      this.log('Agent is in error state, skipping captcha detection setup');
      throw new Error('Agent is in error state - cannot proceed with captcha detection');
    }

    if (this.state.captchaSetupComplete) {
      this.log(
        'Captcha setup already complete, skipping re-initialization. Refreshing bounding box',
      );
      await refreshTensorFlowBoundingBox(
        this.cdpClient,
        executionContextId,
        viewport,
        this.targetSessionId!,
      );
      return;
    }

    this.log('Captcha detected, setting up bindings for screenshots/difference and monitoring');

    const bindingListener = async ({ params }: CDPEvent<CDPRuntimeBindingCalledParams>) => {
      if (params.name === '__captchaSolved__') {
        try {
          const payload = JSON.parse(params.payload);
          if (payload.type === 'CAPTCHA_SOLVED') {
            this.log(`Received CAPTCHA_SOLVED via Binding`);
            // Call the internal handler method
            await this.handleCaptchaSolvedEvent({
              differencePercentage: payload.differencePercentage,
              timestamp: payload.timestamp,
              executionContextId,
              source: payload.source,
            });
          } else {
            console.warn(
              'Received unexpected payload type on __captchaSolved__ binding:',
              payload.type,
            );
          }
        } catch (parseError) {
          console.error(
            'Failed to parse payload from __captchaSolved__ binding:',
            params,
            parseError,
          );
        }
        return;
      }

      console.warn(`Received unhandled binding call: ${JSON.stringify(params)}`);
    };

    try {
      await this.cdpClient.Runtime.addBinding(
        {
          name: '__captchaSolved__',
          executionContextId,
        },
        this.targetSessionId!,
      );
      this.log('Added Runtime binding for captcha solved notifications.');

      this.cdpClient.Runtime.removeEventListener('bindingCalled', bindingListener);
      this.cdpClient.Runtime.addEventListener('bindingCalled', bindingListener);
      this.log('Attached listener for Runtime.bindingCalled');
    } catch (bindingError) {
      console.error('Failed to set up one or more Runtime bindings:', bindingError);
      return;
    }

    this.setState({
      ...this.state,
      status: PageStatus.WAITING_FOR_HUMAN,
      interactivity: {
        ...this.state.interactivity!,
        status: 'enabled',
      },
      captchaSetupComplete: true,
    });

    await startCaptchaMonitoring(
      this.cdpClient,
      {
        diffThreshold: 5,
        screenshotQuality: 90,
      },
      executionContextId,
      this.targetSessionId!,
    );
  }

  private setupCDPErrorMonitoring(): void {
    if (!this.cdpClient) return;

    // Use agent state for user/platform info instead of parsing agent name
    const userId = this.state.userId;
    const platformId = this.state.platformId;
    const referenceId = this.state.referenceId;

    const handleRuntimeException = (event: CDPEvent<CDPRuntimeExceptionParams>) => {
      const exceptionDetails = event.params?.exceptionDetails;

      if (!exceptionDetails.text.includes('kazeel')) return;

      this.log(`[CDP] [DO: ${this.name}] Runtime exception:`, JSON.stringify(event, null, 2));

      const errorContext = ErrorCollector.collectError(
        'cdp',
        'SCRIPT_INJECTION_FAILED',
        event,
        'error',
        {
          userId,
          platformId,
          referenceId,
          sessionId: this.state.sessionId || 'unknown',
        },
      );

      const classifiedError = ErrorRouter.classifyError(errorContext);
      this.handleAndDisplayError(classifiedError);
    };

    const handleConsoleAPI = (event: CDPEvent<CDPConsoleAPIParams>) => {
      // Extract console API details from event.params (simple-cdp event structure)
      const consoleType = event.params?.type;
      const exceptionDetails = event.params?.exceptionDetails;

      if (consoleType === 'error' && !exceptionDetails?.text?.includes('kazeel')) return;

      if (consoleType === 'error') {
        this.log(
          `[CDP] [DO: ${this.name}] Console error detected:`,
          JSON.stringify(event, null, 2),
        );

        const errorContext = ErrorCollector.collectError(
          'cdp',
          'SCRIPT_INJECTION_FAILED',
          event,
          'error',
          {
            userId,
            platformId,
            referenceId,
            sessionId: this.state.sessionId || 'unknown',
          },
        );

        const classifiedError = ErrorRouter.classifyError(errorContext);
        this.handleAndDisplayError(classifiedError);
      }
    };

    // Set up CDP event listeners
    this.cdpClient.Runtime.addEventListener('exceptionThrown', handleRuntimeException);
    this.cdpClient.Runtime.addEventListener('consoleAPICalled', handleConsoleAPI);

    this.cdpErrorHandlers = () => {
      if (this.cdpClient) {
        this.cdpClient.Runtime.removeEventListener('exceptionThrown', handleRuntimeException);
        this.cdpClient.Runtime.removeEventListener('consoleAPICalled', handleConsoleAPI);
      }
    };

    this.log(`[Agent] [DO: ${this.name}] CDP error monitoring enabled`);
  }

  private cleanupCDPErrorMonitoring(): void {
    this.log(`[Agent] [DO: ${this.name}] CDP error monitoring disabled`);
    if (this.cdpErrorHandlers) {
      this.cdpErrorHandlers();
      this.cdpErrorHandlers = null;
    }
  }

  private isCaptchaBoundingBox(obj: unknown): obj is CaptchaBoundingBox {
    return (
      typeof obj === 'object' &&
      obj !== null &&
      typeof (obj as Record<string, unknown>).x === 'number' &&
      typeof (obj as Record<string, unknown>).y === 'number' &&
      typeof (obj as Record<string, unknown>).width === 'number' &&
      typeof (obj as Record<string, unknown>).height === 'number'
    );
  }

  private isBoundingRectArray(obj: unknown): obj is BoundingRect[] {
    return (
      Array.isArray(obj) &&
      obj.every((entry) => {
        return (
          typeof entry.id === 'string' &&
          typeof entry.x === 'number' &&
          typeof entry.y === 'number' &&
          typeof entry.width === 'number' &&
          typeof entry.height === 'number'
        );
      })
    );
  }

  private async cleanupResources(): Promise<void> {
    if (!this.state.sessionId) {
      this.log('No browser session to close');
      return;
    }
    await this.browserService.closeSession(this.state.sessionId);
  }

  async handleRetry(): Promise<void> {
    //TODO: Retry will generate a new link
  }
}
