<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTMX Form Generator Test - Sign in</title>
    <script src="https://unpkg.com/htmx.org@2.0.4"></script>
    <script src="https://unpkg.com/htmx.org/dist/ext/ws.js"></script>
    <link rel="stylesheet" href="../../public/css/styles.css" />
    <link rel="stylesheet" href="../../public/css/video-layout.css" />
    <link rel="stylesheet" href="../../public/css/bottom-sheet.css" />
</head>
<body class="light bg-neutral-60 dark:bg-primary-50 h-screen flex items-center justify-center">
    <div class="relative md:w-[435px] md:rounded-xl shadow-md shadow-neutral-50 bg-surface">
        <div id="connection-flow-wrapper" class="h-screen flex flex-col justify-center items-center relative md:w-[435px] md:h-auto md:rounded-xl shadow-md shadow-neutral-50 bg-surface">
            <div id="connection-flow" class="w-screen flex flex-col items-center justify-center bg-surface text-surface-on-surface md:h-[auto] md:min-h-[600px] md:w-[435px] md:rounded-xl p-8">
                <div class="text-start w-full">
                    <h1 class="text-xl font-semibold mb-4">Sign in</h1>
                    <p class="text-base mb-4"></p>
                </div>
                <form class="form-container" hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML" hx-vals='{"type": "form_submission"}'>
<div class="prompt-container"></div>

    <div class="input-container">
      <div class="floating-input-wrapper">
        <input
          class="floating-input"
          name="Email or phone"
          type="text"
          placeholder=""
          id="Email or phone"
        >
        <label class="floating-label" for="Email or phone">Email or phone</label>
      </div>
    </div>
  

    <div class="input-container">
      <div class="floating-input-wrapper">
        <input
          class="floating-input"
          name="Type the text you hear or see"
          type="text"
          placeholder=""
          id="Type the text you hear or see"
        >
        <label class="floating-label" for="Type the text you hear or see">Type the text you hear or see</label>
      </div>
    </div>
  

    <div class="button-container">
      <button
        type="submit"
        class="button-primary"
        hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML" hx-vals='{"type": "form_submission", "clickId": "Next", "interaction": "submit"}'
      >
        Next
      </button>
    </div>
  
</form>

               <div style="margin-top: 2rem; padding: 1rem; background-color: #f0f0f0; border-radius: 0.5rem; width: 100%;">
  <h3 style="font-size: 0.875rem; font-weight: 600; color: #1c1b1f; margin-bottom: 0.5rem;">
    Form Analysis Data (Simplified Schema):
  </h3>
  <details style="font-size: 0.75rem;">
    <summary style="cursor: pointer; color: #6750a4; margin-bottom: 0.5rem; transition: color 0.2s;"
             onmouseover="this.style.color='#5f4da5'"
             onmouseout="this.style.color='#6750a4'">
      View Raw JSON
    </summary>
    <pre style="margin-top: 0.5rem; padding: 0.5rem; background-color: #ffffff; border: 1px solid #cac4d0; border-radius: 0.25rem; font-size: 0.75rem; overflow: auto; max-height: 10rem; color: #1c1b1f;">
      {
  "extractionResult": {
    "screenInfo": {
      "errors": [],
      "controlVisibilityRules": [
        {
          "id": "Email or phone",
          "status": "included",
          "reason": "Visible email or phone input field."
        },
        {
          "id": "Type the text you hear or see",
          "status": "included",
          "reason": "Visible text input for CAPTCHA."
        },
        {
          "id": "Next",
          "status": "included",
          "reason": "Primary button to proceed with authentication."
        }
      ]
    },
    "controls": {
      "fields": [
        {
          "isLikelyDropdownReason": "The control is an input field for email or phone, not a dropdown.",
          "id": "Email or phone",
          "order": 1,
          "label": "Email or phone",
          "isLikelyDropdown": false,
          "fieldControlType": "text",
          "actiontype": "fill",
          "name": "Email or phone",
          "checked": false,
          "isDontAskAgainControl": false
        },
        {
          "isLikelyDropdownReason": "The control is an input field for text, not a dropdown.",
          "id": "Type the text you hear or see",
          "order": 3,
          "label": "Type the text you hear or see",
          "isLikelyDropdown": false,
          "fieldControlType": "text",
          "actiontype": "fill",
          "name": "Type the text you hear or see",
          "checked": false,
          "isDontAskAgainControl": false
        }
      ],
      "buttons": [
        {
          "id": "Next",
          "order": 4,
          "label": "Next",
          "variant": "primary",
          "type": "submit",
          "actiontype": "click",
          "isDontAskAgainControl": false
        }
      ]
    }
  },
  "classificationResult": {
    "screenInfo": {
      "classificationReasoning": "The screen contains a CAPTCHA element, which is used to distinguish humans from bots.",
      "authStateReasoning": "CAPTCHAs are typically presented during login or signup flows, indicating the user is not yet authenticated.",
      "screenClass": "captcha-screen",
      "instruction": "Type the text you hear or see.",
      "title": "Sign in",
      "authState": "not-authenticated",
      "errors": null,
      "verificationCode": null
    }
  }
}
    </pre>
  </details>
</div>

            </div>
        </div>
    </div>
</body>
</html>